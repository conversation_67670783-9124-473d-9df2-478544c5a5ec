import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:novel_app/controllers/novel_controller.dart';
import 'package:novel_app/controllers/api_config_controller.dart';
import 'package:novel_app/models/novel.dart';
import 'package:novel_app/models/chat_message.dart' as chat_model;
import 'package:novel_app/models/chat_session.dart';
import 'package:novel_app/services/chat_history_service.dart';
import 'package:novel_app/services/novel_vectorization_service.dart';
import 'package:novel_app/langchain/models/novel_memory.dart';
import 'package:novel_app/screens/ai_chat/chat_sessions_screen.dart';
import 'package:novel_app/widgets/themed_dropdown.dart';
import 'dart:convert' show jsonEncode, jsonDecode, utf8, LineSplitter;
import 'package:http/http.dart' as http;
import 'package:novel_app/models/model_config.dart' as model_config;
import 'package:novel_app/utils/network_client.dart';
import 'package:flutter_markdown/flutter_markdown.dart';

class ChatMessage {
  String content; // 改为非final，允许更新内容
  final bool isUser;
  final DateTime timestamp;
  final bool isError;

  ChatMessage({
    required this.content,
    required this.isUser,
    required this.timestamp,
    this.isError = false,
  });
}

class DaizongAIController extends GetxController {
  final NovelController _novelController = Get.find<NovelController>();
  final ApiConfigController _apiConfigController =
      Get.find<ApiConfigController>();
  final ChatHistoryService _chatHistoryService = Get.find<ChatHistoryService>();

  final RxBool isGenerating = false.obs;
  final RxList<ChatMessage> messages = <ChatMessage>[].obs;
  final TextEditingController textController = TextEditingController();
  final ScrollController scrollController = ScrollController();

  // 当前聊天模式: 'normal' 或 'novel'
  final RxString chatMode = 'normal'.obs;

  // 当前选择的小说（用于小说对话模式）
  final Rx<Novel?> selectedNovel = Rx<Novel?>(null);

  // 当前会话
  final Rx<ChatSession?> currentSession = Rx<ChatSession?>(null);

  // 所有会话
  final RxList<ChatSession> allSessions = <ChatSession>[].obs;

  // 聊天上下文
  final List<Map<String, String>> _chatContext = [];

  // HTTP客户端 - 使用系统代理配置
  late final http.Client _client;

  @override
  void onInit() {
    super.onInit();

    // 初始化HTTP客户端 - 对于Google API使用系统代理配置
    final apiConfig = Get.find<ApiConfigController>();
    final currentModel = apiConfig.getCurrentModel();

    if (currentModel.apiFormat == 'Google API') {
      _client = NetworkClient.createSystemProxyGoogleApiClient();
      print('🌐🌐🌐 聊天功能强制使用系统代理客户端 🌐🌐🌐');
    } else {
      _client = NetworkClient.createSystemClient();
      print('聊天功能使用系统网络客户端');
    }

    // 延迟初始化，避免在构建期间触发响应式更新
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // 加载所有会话
      _loadSessions();

      // 创建新会话或加载最近的会话
      _createOrLoadSession();
    });
  }

  /// 加载所有会话
  void _loadSessions() {
    try {
      print('[DaizongAIController] 开始加载会话列表');

      // 从ChatHistoryService获取所有会话
      final sessions = _chatHistoryService.sessions;
      print('[DaizongAIController] 从服务获取到 ${sessions.length} 个会话');

      // 更新会话列表
      allSessions.assignAll(sessions);
      print('[DaizongAIController] 已加载 ${allSessions.length} 个会话');
    } catch (e) {
      print('[DaizongAIController] 加载会话失败: $e');
      // 清空会话列表，避免显示错误数据
      allSessions.clear();
    }
  }

  /// 创建新会话或加载最近的会话
  Future<void> _createOrLoadSession() async {
    try {
      // 如果有会话，加载最近的会话
      if (allSessions.isNotEmpty) {
        try {
          await loadSession(allSessions.first);
        } catch (e) {
          print('[DaizongAIController] 加载会话失败，将创建新会话: $e');
          // 如果加载失败，尝试创建新会话
          await createNewSession('新对话');
        }
      } else {
        // 否则创建新会话
        await createNewSession('新对话');
      }
    } catch (e) {
      print('[DaizongAIController] 创建或加载会话失败: $e');

      // 如果加载失败，创建临时会话（不保存）
      _initializeTemporarySession();
    }
  }

  /// 初始化临时会话（不保存到数据库）
  void _initializeTemporarySession() {
    // 添加欢迎消息
    messages.add(ChatMessage(
      content: '您好，我是岱宗AI，可以为您提供创作助手服务。我可以帮助您进行小说创作、角色设计、情节构思等工作。请问有什么可以帮您的？',
      isUser: false,
      timestamp: DateTime.now(),
    ));

    // 初始化聊天上下文
    _chatContext.clear();
    _chatContext.add({
      "role": "system",
      "content":
          "你是岱宗AI，一个专业的小说创作助手。你擅长帮助用户进行小说创作、角色设计、情节构思等工作。回答要专业、有创意且符合中文写作习惯。"
    });

    _chatContext.add({
      "role": "assistant",
      "content": "您好，我是岱宗AI，可以为您提供创作助手服务。我可以帮助您进行小说创作、角色设计、情节构思等工作。请问有什么可以帮您的？"
    });
  }

  @override
  void onClose() {
    textController.dispose();
    scrollController.dispose();
    _client.close();
    super.onClose();
  }

  /// 创建新会话
  Future<void> createNewSession(String title) async {
    try {
      // 创建新的普通聊天会话
      final session = await _chatHistoryService.createNormalChatSession(title);

      // 设置当前会话
      currentSession.value = session;

      // 清空消息列表和聊天上下文
      messages.clear();
      _chatContext.clear();

      // 添加欢迎消息
      final welcomeMessage = ChatMessage(
        content: '您好，我是岱宗AI，可以为您提供创作助手服务。我可以帮助您进行小说创作、角色设计、情节构思等工作。请问有什么可以帮您的？',
        isUser: false,
        timestamp: DateTime.now(),
      );
      messages.add(welcomeMessage);

      // 初始化聊天上下文
      _chatContext.add({
        "role": "system",
        "content":
            "你是岱宗AI，一个专业的小说创作助手。你擅长帮助用户进行小说创作、角色设计、情节构思等工作。回答要专业、有创意且符合中文写作习惯。"
      });

      _chatContext.add({
        "role": "assistant",
        "content":
            "您好，我是岱宗AI，可以为您提供创作助手服务。我可以帮助您进行小说创作、角色设计、情节构思等工作。请问有什么可以帮您的？"
      });

      // 保存欢迎消息到数据库
      try {
        await _chatHistoryService.addAIReply(
          welcomeMessage.content,
          sessionId: session.id,
        );
      } catch (e) {
        print('[DaizongAIController] 保存欢迎消息失败，但会话已创建: $e');
      }

      // 重新加载会话列表
      _loadSessions();

      print('[DaizongAIController] 已创建新会话: ${session.title}');
    } catch (e) {
      print('[DaizongAIController] 创建新会话失败: $e');
      // 如果创建会话失败，创建临时会话
      _initializeTemporarySession();
    }
  }

  /// 加载会话
  Future<void> loadSession(ChatSession session) async {
    try {
      // 设置当前会话
      currentSession.value = session;

      // 加载会话历史
      await _chatHistoryService.loadSessionHistory(session.id);

      // 将ChatHistoryService中的消息转换为本地消息格式
      final convertedMessages = _chatHistoryService.messages
          .map((msg) => ChatMessage(
                content: msg.content,
                isUser: msg.type == chat_model.ChatMessageType.user,
                timestamp: msg.timestamp,
                isError: false,
              ))
          .toList();

      // 更新消息列表
      messages.assignAll(convertedMessages);

      // 重建聊天上下文
      _rebuildChatContext();

      // 如果消息为空，添加欢迎消息
      if (messages.isEmpty) {
        final welcomeMessage = ChatMessage(
          content:
              '您好，我是岱宗AI，可以为您提供创作助手服务。我可以帮助您进行小说创作、角色设计、情节构思等工作。请问有什么可以帮您的？',
          isUser: false,
          timestamp: DateTime.now(),
        );
        messages.add(welcomeMessage);

        // 保存欢迎消息到数据库
        await _chatHistoryService.addAIReply(
          welcomeMessage.content,
          sessionId: session.id,
        );
      }

      // 滚动到底部
      _scrollToBottom();

      print('[DaizongAIController] 已加载会话: ${session.title}');
    } catch (e) {
      print('[DaizongAIController] 加载会话失败: $e');

      // 如果加载失败，创建临时会话
      _initializeTemporarySession();
    }
  }

  /// 删除会话
  Future<void> deleteSession(ChatSession session) async {
    try {
      // 删除会话
      await _chatHistoryService.deleteSession(session.id);

      // 重新加载会话列表
      _loadSessions();

      // 如果删除的是当前会话，创建新会话
      if (currentSession.value?.id == session.id) {
        if (allSessions.isNotEmpty) {
          await loadSession(allSessions.first);
        } else {
          await createNewSession('新对话');
        }
      }

      print('[DaizongAIController] 已删除会话: ${session.title}');
    } catch (e) {
      print('[DaizongAIController] 删除会话失败: $e');
    }
  }

  /// 重建聊天上下文
  void _rebuildChatContext() {
    _chatContext.clear();

    // 添加系统消息
    _chatContext.add({
      "role": "system",
      "content":
          "你是岱宗AI，一个专业的小说创作助手。你擅长帮助用户进行小说创作、角色设计、情节构思等工作。回答要专业、有创意且符合中文写作习惯。"
    });

    // 添加历史消息（最多20条）
    final historyMessages = messages.length > 20
        ? messages.sublist(messages.length - 20)
        : messages;

    for (final msg in historyMessages) {
      _chatContext.add({
        "role": msg.isUser ? "user" : "assistant",
        "content": msg.content,
      });
    }
  }

  // 切换聊天模式
  void switchChatMode(String mode) {
    if (mode != chatMode.value) {
      chatMode.value = mode;
      if (mode == 'normal') {
        messages.add(ChatMessage(
          content: '已切换到普通聊天模式。我可以帮助您进行小说创作、角色设计、情节构思等工作。',
          isUser: false,
          timestamp: DateTime.now(),
        ));

        // 重置聊天上下文
        _chatContext.clear();
        _chatContext.add({
          "role": "system",
          "content":
              "你是岱宗AI，一个专业的小说创作助手。你擅长帮助用户进行小说创作、角色设计、情节构思等工作。回答要专业、有创意且符合中文写作习惯。"
        });
        _chatContext.add({
          "role": "assistant",
          "content": "已切换到普通聊天模式。我可以帮助您进行小说创作、角色设计、情节构思等工作。"
        });
      } else {
        messages.add(ChatMessage(
          content: '已切换到小说对话模式。请选择一部小说进行对话。',
          isUser: false,
          timestamp: DateTime.now(),
        ));

        // 重置聊天上下文
        _chatContext.clear();
        _chatContext.add({
          "role": "system",
          "content":
              "你是岱宗AI，一个专业的小说创作助手。你现在处于小说对话模式，可以帮助用户分析小说中的角色、情节、主题等内容，并提供创作建议。回答要专业、有创意且符合中文写作习惯。"
        });
        _chatContext
            .add({"role": "assistant", "content": "已切换到小说对话模式。请选择一部小说进行对话。"});
      }

      _scrollToBottom();
    }
  }

  // 选择小说
  Future<void> selectNovel(Novel novel) async {
    selectedNovel.value = novel;

    // 显示加载状态
    messages.add(ChatMessage(
      content: '正在加载《${novel.title}》的内容，请稍候...',
      isUser: false,
      timestamp: DateTime.now(),
    ));
    _scrollToBottom();

    try {
      // 获取向量化服务
      final vectorizationService = Get.find<NovelVectorizationService>();
      final apiConfigController = Get.find<ApiConfigController>();

      // 构建基础小说信息摘要
      String novelSummary = '小说标题：${novel.title}\n';
      novelSummary += '类型：${novel.genre}\n';
      if (novel.outline.isNotEmpty) {
        novelSummary += '大纲：${novel.outline}\n';
      }

      // 获取小说的完整内容
      String fullNovelContent = '';
      if (apiConfigController.embeddingModel.value.enabled) {
        try {
          // 检查小说是否已向量化
          if (!vectorizationService.isNovelVectorized(novel.title)) {
            // 更新状态消息
            messages.removeLast();
            messages.add(ChatMessage(
              content: '正在向量化《${novel.title}》的内容，这可能需要一些时间...',
              isUser: false,
              timestamp: DateTime.now(),
            ));
            _scrollToBottom();

            // 向量化小说
            final vectorizeResult = await vectorizationService.vectorizeNovel(novel.title);
            if (!vectorizeResult) {
              print('[DaizongAI] 小说向量化失败，将使用基础模式');
            }
          }

          // 获取小说的完整内容用于上下文
          final novelMemory = NovelMemory(novelTitle: novel.title);
          fullNovelContent = await novelMemory.getAllNovelContent();

          // 限制内容长度以避免上下文过长
          if (fullNovelContent.length > 8000) {
            fullNovelContent = fullNovelContent.substring(0, 8000) + '\n...(内容已截断)';
          }

          novelSummary += '\n# 小说内容概览\n${fullNovelContent.substring(0, fullNovelContent.length > 2000 ? 2000 : fullNovelContent.length)}';
          if (fullNovelContent.length > 2000) {
            novelSummary += '\n...(更多内容将根据对话需要动态获取)';
          }
        } catch (e) {
          print('[DaizongAI] 获取小说内容失败: $e');
        }
      }

      // 移除加载消息
      messages.removeLast();

      // 添加成功消息
      String statusMessage = '已选择《${novel.title}》。';
      if (apiConfigController.embeddingModel.value.enabled &&
          vectorizationService.isNovelVectorized(novel.title)) {
        statusMessage += '已启用智能检索功能，我可以基于小说的具体内容与您进行深入讨论。';
      } else {
        statusMessage += '您可以就这部小说的情节、角色、主题等方面与我进行讨论。';
      }

      messages.add(ChatMessage(
        content: statusMessage,
        isUser: false,
        timestamp: DateTime.now(),
      ));

      // 更新聊天上下文
      _chatContext.clear();
      _chatContext.add({
        "role": "system",
        "content": "你是岱宗AI，一个专业的小说创作助手。用户已选择了小说《${novel.title}》进行对话。以下是小说的相关信息：\n$novelSummary\n\n请基于这些信息与用户进行对话，帮助用户分析和改进小说。当用户询问具体情节时，你可以引用相关的章节内容。回答要专业、有创意且符合中文写作习惯。"
      });
      _chatContext.add({
        "role": "assistant",
        "content": statusMessage
      });

    } catch (e) {
      // 移除加载消息
      if (messages.isNotEmpty && messages.last.content.contains('正在加载')) {
        messages.removeLast();
      }

      // 添加错误消息
      messages.add(ChatMessage(
        content: '加载《${novel.title}》时出现错误，将使用基础模式进行对话。',
        isUser: false,
        timestamp: DateTime.now(),
        isError: true,
      ));

      // 使用基础模式
      String basicSummary = '小说标题：${novel.title}\n类型：${novel.genre}\n';
      if (novel.outline.isNotEmpty) {
        basicSummary += '大纲：${novel.outline}\n';
      }

      _chatContext.clear();
      _chatContext.add({
        "role": "system",
        "content": "你是岱宗AI，一个专业的小说创作助手。用户已选择了小说《${novel.title}》进行对话。以下是小说的基本信息：\n$basicSummary\n请基于这些信息与用户进行对话。"
      });
      _chatContext.add({
        "role": "assistant",
        "content": "已选择《${novel.title}》。您可以就这部小说的情节、角色、主题等方面与我进行讨论。"
      });
    }

    _scrollToBottom();
  }

  // 发送消息并与AI交互
  Future<void> sendMessage(String content) async {
    if (content.trim().isEmpty || isGenerating.value) return;

    // 清空输入框
    textController.clear();

    // 添加用户消息
    final userMessage = ChatMessage(
      content: content,
      isUser: true,
      timestamp: DateTime.now(),
    );
    messages.add(userMessage);

    // 滚动到底部
    _scrollToBottom();

    // 设置生成状态
    isGenerating.value = true;

    // 如果有当前会话，保存用户消息到数据库
    if (currentSession.value != null) {
      try {
        await _chatHistoryService.continueChat(
          content,
        );
      } catch (e) {
        print('[DaizongAIController] 保存用户消息失败: $e');
      }
    }

    // 增强聊天上下文 - 如果选择了小说且启用了嵌入模型，添加相关内容
    String enhancedContent = content;
    if (selectedNovel.value != null) {
      try {
        final vectorizationService = Get.find<NovelVectorizationService>();
        final apiConfigController = Get.find<ApiConfigController>();

        if (apiConfigController.embeddingModel.value.enabled &&
            vectorizationService.isNovelVectorized(selectedNovel.value!.title)) {

          // 构建优化的查询文本
          final queryText = _buildChatQueryText(selectedNovel.value!.title, content);

          // 获取与用户问题最相关的小说内容
          final searchResults = await vectorizationService.searchNovelContent(
            selectedNovel.value!.title,
            queryText,
            maxResults: apiConfigController.embeddingModel.value.topK,
          );

          if (searchResults.isNotEmpty) {
            // 过滤高相似度的结果
            final filteredResults = searchResults
                .where((result) =>
                    (result['similarity'] as double) >=
                    apiConfigController.embeddingModel.value.similarityThreshold)
                .toList();

            if (filteredResults.isNotEmpty) {
              final relatedContent = StringBuffer();
              relatedContent.writeln('\n# 相关小说内容');

              for (final result in filteredResults.take(3)) { // 限制为前3个最相关的结果
                final resultContent = result['content'] as String;
                final title = result['title'] as String;
                final similarity = result['similarity'] as double;

                relatedContent.writeln('## $title (相似度: ${(similarity * 100).toStringAsFixed(1)}%)');
                relatedContent.writeln(resultContent.length > 500
                    ? '${resultContent.substring(0, 500)}...'
                    : resultContent);
                relatedContent.writeln();
              }

              enhancedContent = '$content\n\n${relatedContent.toString()}';
              print('[DaizongAI] 已添加 ${filteredResults.length} 个相关内容片段');
            }
          }
        }
      } catch (e) {
        print('[DaizongAI] 获取相关内容失败: $e');
        // 继续使用原始内容
      }
    }

    // 更新聊天上下文
    _chatContext.add({"role": "user", "content": enhancedContent});

    try {
      // 获取当前模型配置
      final modelConfig = Get.find<ApiConfigController>().getCurrentModel();

      // 检查是否是通义千问模型
      final isQwen = modelConfig.name.toLowerCase().contains('qwen') ||
          modelConfig.model.toLowerCase().contains('qwen');

      // 对于通义千问模型，强制使用流式输出
      final useStream = isQwen || modelConfig.name.contains('通义');

      if (useStream) {
        // 使用流式输出模式
        await _sendStreamRequest(content, modelConfig);
      } else {
        // 使用普通请求模式
        await _sendNormalRequest(content, modelConfig);
      }
    } catch (e) {
      // 处理异常
      final errorMessage = ChatMessage(
        content: '发生错误: $e',
        isUser: false,
        timestamp: DateTime.now(),
        isError: true,
      );
      messages.add(errorMessage);

      // 如果有当前会话，保存错误消息到数据库
      if (currentSession.value != null) {
        try {
          await _chatHistoryService.addAIReply(
            errorMessage.content,
          );
        } catch (e) {
          print('[DaizongAIController] 保存错误消息失败: $e');
        }
      }
    } finally {
      isGenerating.value = false;
      _scrollToBottom();
    }
  }

  // 使用流式输出模式发送请求
  Future<void> _sendStreamRequest(String content, dynamic modelConfig) async {
    try {
      // 准备请求数据
      final Map<String, dynamic> requestData = {};
      final Map<String, String> headers = {
        'Content-Type': 'application/json; charset=utf-8',
        'Accept': 'text/event-stream',
      };

      // 根据不同的API格式构建请求
      if (modelConfig.apiFormat == 'OpenAI API兼容') {
        // OpenAI兼容格式
        requestData['model'] = modelConfig.model;
        requestData['messages'] = _chatContext;
        requestData['temperature'] = modelConfig.temperature;
        requestData['max_tokens'] = modelConfig.maxTokens;
        requestData['top_p'] = modelConfig.topP;
        requestData['stream'] = true; // 启用流式输出

        if (modelConfig.apiKey.isNotEmpty) {
          headers['Authorization'] = 'Bearer ${modelConfig.apiKey}';
        }

        // 如果是百度千帆，添加appId
        if (modelConfig.name.contains('百度') && modelConfig.appId.isNotEmpty) {
          headers['X-Bce-App-Id'] = modelConfig.appId;
        }
      } else if (modelConfig.apiFormat == 'Google API') {
        // Google API格式 - 不支持system角色，需要特殊处理
        List<Map<String, dynamic>> contents = [];
        String combinedContent = '';

        // 合并所有消息内容，因为Google API不支持多角色对话
        for (var msg in _chatContext) {
          if (msg['role'] == 'system') {
            // 将系统消息作为前缀
            combinedContent = '${msg['content']}\n\n$combinedContent';
          } else if (msg['role'] == 'user') {
            combinedContent += 'User: ${msg['content']}\n';
          } else if (msg['role'] == 'assistant') {
            combinedContent += 'Assistant: ${msg['content']}\n';
          }
        }

        // 创建单个用户消息
        contents.add({
          'parts': [
            {'text': combinedContent.trim()}
          ]
        });

        requestData['contents'] = contents;
        requestData['generationConfig'] = {
          'temperature': modelConfig.temperature,
          'topP': modelConfig.topP,
          'maxOutputTokens': modelConfig.maxTokens,
        };
        // Google API不支持stream参数，流式处理通过streamGenerateContent端点实现

        if (modelConfig.apiKey.isNotEmpty) {
          headers['x-goog-api-key'] = modelConfig.apiKey;
        }
      } else {
        // 默认使用OpenAI格式
        requestData['model'] = modelConfig.model;
        requestData['messages'] = _chatContext;
        requestData['temperature'] = modelConfig.temperature;
        requestData['max_tokens'] = modelConfig.maxTokens;
        requestData['stream'] = true; // 启用流式输出

        if (modelConfig.apiKey.isNotEmpty) {
          headers['Authorization'] = 'Bearer ${modelConfig.apiKey}';
        }
      }

      // 发送请求
      final url = '${modelConfig.apiUrl}${modelConfig.apiPath}';
      final request = http.Request('POST', Uri.parse(url));
      request.headers.addAll(headers);
      request.body = jsonEncode(requestData);

      final streamedResponse = await _client.send(request);

      if (streamedResponse.statusCode != 200) {
        // 处理错误
        final response = await http.Response.fromStream(streamedResponse);
        String errorBody;
        try {
          errorBody = utf8.decode(response.bodyBytes);
        } catch (e) {
          errorBody = response.body;
        }

        messages.add(ChatMessage(
          content: '请求失败，状态码: ${response.statusCode}\n$errorBody',
          isUser: false,
          timestamp: DateTime.now(),
          isError: true,
        ));
        return;
      }

      // 创建一个空的AI消息
      final aiMessage = ChatMessage(
        content: '',
        isUser: false,
        timestamp: DateTime.now(),
      );
      messages.add(aiMessage);

      // 处理流式响应
      final stream = streamedResponse.stream
          .transform(utf8.decoder)
          .transform(const LineSplitter());

      await for (var line in stream) {
        if (line.trim().isEmpty) continue;
        if (line.startsWith('data: ')) {
          line = line.substring(6);
          if (line == '[DONE]') break;

          try {
            final data = jsonDecode(line);
            String chunk = '';

            // 根据不同的API格式解析响应
            if (modelConfig.apiFormat == 'OpenAI API兼容') {
              if (data['choices'] != null &&
                  data['choices'][0]['delta'] != null &&
                  data['choices'][0]['delta']['content'] != null) {
                chunk = data['choices'][0]['delta']['content'];
              }
            } else if (modelConfig.apiFormat == 'Google API') {
              if (data['candidates'] != null &&
                  data['candidates'][0]['content'] != null &&
                  data['candidates'][0]['content']['parts'] != null &&
                  data['candidates'][0]['content']['parts'][0]['text'] !=
                      null) {
                chunk = data['candidates'][0]['content']['parts'][0]['text'];
              }
            }

            if (chunk.isNotEmpty) {
              // 更新AI消息内容
              aiMessage.content += chunk;
              // 强制UI更新
              messages.refresh();
              // 滚动到底部
              _scrollToBottom();
            }
          } catch (e) {
            print('解析流式响应出错: $e');
          }
        }
      }

      // 更新聊天上下文
      _chatContext.add({"role": "assistant", "content": aiMessage.content});

      // 如果有当前会话，保存AI回复到数据库
      if (currentSession.value != null) {
        try {
          await _chatHistoryService.addAIReply(
            aiMessage.content,
          );
        } catch (e) {
          print('[DaizongAIController] 保存AI回复失败: $e');
        }
      }
    } catch (e) {
      messages.add(ChatMessage(
        content: '流式请求失败: $e',
        isUser: false,
        timestamp: DateTime.now(),
        isError: true,
      ));
    }
  }

  // 使用普通模式发送请求
  Future<void> _sendNormalRequest(String content, dynamic modelConfig) async {
    // 准备请求数据
    final Map<String, dynamic> requestData = {};
    final Map<String, String> headers = {
      'Content-Type': 'application/json; charset=utf-8',
      'Accept': 'application/json; charset=utf-8',
    };

    // 根据不同的API格式构建请求
    if (modelConfig.apiFormat == 'OpenAI API兼容') {
      // OpenAI兼容格式
      requestData['model'] = modelConfig.model;
      requestData['messages'] = _chatContext;
      requestData['temperature'] = modelConfig.temperature;
      requestData['max_tokens'] = modelConfig.maxTokens;
      requestData['top_p'] = modelConfig.topP;
      requestData['stream'] = false;

      if (modelConfig.apiKey.isNotEmpty) {
        headers['Authorization'] = 'Bearer ${modelConfig.apiKey}';
      }

      // 如果是百度千帆，添加appId
      if (modelConfig.name.contains('百度') && modelConfig.appId.isNotEmpty) {
        headers['X-Bce-App-Id'] = modelConfig.appId;
      }
    } else if (modelConfig.apiFormat == 'Google API') {
      // Google API格式 - 不支持system角色，需要特殊处理
      List<Map<String, dynamic>> contents = [];
      String combinedContent = '';

      // 合并所有消息内容，因为Google API不支持多角色对话
      for (var msg in _chatContext) {
        if (msg['role'] == 'system') {
          // 将系统消息作为前缀
          combinedContent = '${msg['content']}\n\n$combinedContent';
        } else if (msg['role'] == 'user') {
          combinedContent += 'User: ${msg['content']}\n';
        } else if (msg['role'] == 'assistant') {
          combinedContent += 'Assistant: ${msg['content']}\n';
        }
      }

      // 创建单个用户消息
      contents.add({
        'parts': [
          {'text': combinedContent.trim()}
        ]
      });

      requestData['contents'] = contents;
      requestData['generationConfig'] = {
        'temperature': modelConfig.temperature,
        'topP': modelConfig.topP,
        'maxOutputTokens': modelConfig.maxTokens,
      };

      if (modelConfig.apiKey.isNotEmpty) {
        headers['x-goog-api-key'] = modelConfig.apiKey;
      }
    } else {
      // 默认使用OpenAI格式
      requestData['model'] = modelConfig.model;
      requestData['messages'] = _chatContext;
      requestData['temperature'] = modelConfig.temperature;
      requestData['max_tokens'] = modelConfig.maxTokens;

      if (modelConfig.apiKey.isNotEmpty) {
        headers['Authorization'] = 'Bearer ${modelConfig.apiKey}';
      }
    }

    // 发送请求
    final url = '${modelConfig.apiUrl}${modelConfig.apiPath}';
    final response = await _client
        .post(
          Uri.parse(url),
          headers: headers,
          body: jsonEncode(requestData),
        )
        .timeout(Duration(seconds: modelConfig.timeout));

    // 处理响应
    if (response.statusCode == 200) {
      // 确保使用UTF-8解码响应内容
      final responseData = jsonDecode(utf8.decode(response.bodyBytes));
      String aiResponse = '';

      // 根据不同的API格式解析响应
      if (modelConfig.apiFormat == 'OpenAI API兼容') {
        aiResponse = responseData['choices'][0]['message']['content'];
      } else if (modelConfig.apiFormat == 'Google API') {
        aiResponse =
            responseData['candidates'][0]['content']['parts'][0]['text'];
      } else {
        // 默认尝试OpenAI格式
        aiResponse = responseData['choices'][0]['message']['content'];
      }

      // 添加AI响应到消息列表
      final aiMessage = ChatMessage(
        content: aiResponse,
        isUser: false,
        timestamp: DateTime.now(),
      );
      messages.add(aiMessage);

      // 更新聊天上下文
      _chatContext.add({"role": "assistant", "content": aiResponse});

      // 如果有当前会话，保存AI回复到数据库
      if (currentSession.value != null) {
        try {
          await _chatHistoryService.addAIReply(
            aiResponse,
          );
        } catch (e) {
          print('[DaizongAIController] 保存AI回复失败: $e');
        }
      }
    } else {
      // 处理错误
      String errorBody;
      try {
        // 尝试使用UTF-8解码错误消息
        errorBody = utf8.decode(response.bodyBytes);
      } catch (e) {
        // 如果解码失败，使用原始消息
        errorBody = response.body;
      }

      final errorMessage = ChatMessage(
        content: '请求失败，状态码: ${response.statusCode}\n$errorBody',
        isUser: false,
        timestamp: DateTime.now(),
        isError: true,
      );
      messages.add(errorMessage);
    }
  }

  // 滚动到底部
  void _scrollToBottom() {
    if (scrollController.hasClients) {
      Future.delayed(const Duration(milliseconds: 100), () {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      });
    }
  }

  // 构建聊天查询文本
  String _buildChatQueryText(String novelTitle, String userMessage) {
    final buffer = StringBuffer();

    // 添加小说标题
    buffer.writeln('小说：$novelTitle');

    // 添加用户问题
    buffer.writeln('用户问题：$userMessage');

    // 分析用户问题类型，添加相关关键词
    final lowerMessage = userMessage.toLowerCase();

    if (lowerMessage.contains('角色') || lowerMessage.contains('人物') || lowerMessage.contains('主角') || lowerMessage.contains('配角')) {
      buffer.writeln('关键词：角色分析 人物设定 角色关系 人物性格');
    } else if (lowerMessage.contains('情节') || lowerMessage.contains('剧情') || lowerMessage.contains('故事') || lowerMessage.contains('发展')) {
      buffer.writeln('关键词：情节发展 故事线 剧情转折 情节冲突');
    } else if (lowerMessage.contains('主题') || lowerMessage.contains('思想') || lowerMessage.contains('意义') || lowerMessage.contains('内涵')) {
      buffer.writeln('关键词：主题思想 深层含义 价值观念 思想内涵');
    } else if (lowerMessage.contains('结局') || lowerMessage.contains('结尾') || lowerMessage.contains('结果')) {
      buffer.writeln('关键词：故事结局 情节结尾 最终结果');
    } else if (lowerMessage.contains('开头') || lowerMessage.contains('开始') || lowerMessage.contains('起因')) {
      buffer.writeln('关键词：故事开头 情节起因 故事背景');
    } else {
      buffer.writeln('关键词：小说内容 相关情节');
    }

    return buffer.toString();
  }
}

class DaizongAIScreen extends GetView<DaizongAIController> {
  const DaizongAIScreen({super.key});

  @override
  DaizongAIController get controller => Get.put(DaizongAIController());

  // 定义主题色
  static const Color earthYellow = Color(0xFFD4B483);
  static const Color forestGreen = Color(0xFF3A6B35);
  static const Color lightGreen = Color(0xFF93B884);
  static const Color lightYellow = Color(0xFFF2E3BC);

  /// 显示会话列表页面
  Future<void> _showSessionsScreen(BuildContext context) async {
    // 导入会话列表页面
    final ChatSession? selectedSession =
        await Get.to(() => const ChatSessionsScreen());

    // 如果用户选择了会话，加载该会话
    if (selectedSession != null) {
      final controller = Get.find<DaizongAIController>();
      await controller.loadSession(selectedSession);
    }
  }

  /// 创建新会话
  Future<void> _createNewSession(BuildContext context) async {
    // 显示对话框，让用户输入会话名称
    final TextEditingController nameController = TextEditingController();

    final bool? confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('创建新会话'),
        content: TextField(
          controller: nameController,
          decoration: const InputDecoration(
            hintText: '请输入会话名称',
          ),
          autofocus: true,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('取消'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('创建'),
          ),
        ],
      ),
    );

    if (confirmed == true && nameController.text.trim().isNotEmpty) {
      final controller = Get.find<DaizongAIController>();
      await controller.createNewSession(nameController.text.trim());
    }

    nameController.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        title: const Text('岱宗AI'),
        backgroundColor: forestGreen,
        foregroundColor: Colors.white,
        elevation: 0,
        actions: [
          // 会话管理按钮
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: () => _showSessionsScreen(context),
            tooltip: '会话历史',
          ),
          // 新建会话按钮
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () => _createNewSession(context),
            tooltip: '新建会话',
          ),
        ],
      ),
      body: Column(
        children: [
          // 模式选择器
          Container(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            decoration: BoxDecoration(
              color: isDark ? Colors.grey.shade800 : lightYellow,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(13),
                  blurRadius: 2,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                Obx(() => Expanded(
                      child: SegmentedButton<String>(
                        segments: const [
                          ButtonSegment<String>(
                            value: 'normal',
                            label: Text('普通聊天'),
                            icon: Icon(Icons.chat_bubble_outline),
                          ),
                          ButtonSegment<String>(
                            value: 'novel',
                            label: Text('小说对话'),
                            icon: Icon(Icons.book_outlined),
                          ),
                        ],
                        selected: {controller.chatMode.value},
                        onSelectionChanged: (Set<String> selection) {
                          controller.switchChatMode(selection.first);
                        },
                        style: ButtonStyle(
                          backgroundColor:
                              WidgetStateProperty.resolveWith<Color>(
                            (states) {
                              if (states.contains(WidgetState.selected)) {
                                return forestGreen;
                              }
                              return isDark
                                  ? Colors.grey.shade700
                                  : Colors.white;
                            },
                          ),
                          foregroundColor:
                              WidgetStateProperty.resolveWith<Color>(
                            (states) {
                              if (states.contains(WidgetState.selected)) {
                                return Colors.white;
                              }
                              return isDark ? Colors.white : Colors.black87;
                            },
                          ),
                        ),
                      ),
                    )),

                // 小说选择器（仅在小说对话模式下显示）
                Obx(
                  () => controller.chatMode.value == 'novel'
                      ? Padding(
                          padding: const EdgeInsets.only(left: 16.0),
                          child: _buildNovelSelector(context, controller),
                        )
                      : const SizedBox.shrink(),
                ),
              ],
            ),
          ),

          // 消息列表
          Expanded(
            child: Container(
              color: isDark ? Colors.grey.shade900 : lightYellow.withAlpha(51),
              child: Obx(() => ListView.builder(
                    controller: controller.scrollController,
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    itemCount: controller.messages.length,
                    itemBuilder: (context, index) {
                      final message = controller.messages[index];
                      return _buildMessageBubble(context, message, isDark);
                    },
                  )),
            ),
          ),

          // 输入框区域
          Container(
            decoration: BoxDecoration(
              color: isDark ? Colors.grey.shade800 : Colors.white,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(26),
                  blurRadius: 4,
                  offset: const Offset(0, -1),
                ),
              ],
            ),
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: controller.textController,
                    maxLines: null,
                    textInputAction: TextInputAction.send,
                    decoration: InputDecoration(
                      hintText: '输入消息...',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(24),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: isDark
                          ? Colors.grey.shade700
                          : lightYellow.withAlpha(77),
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                    ),
                    onSubmitted: (value) {
                      if (value.trim().isNotEmpty) {
                        controller.sendMessage(value);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 8),
                Obx(() => Material(
                      color: forestGreen,
                      borderRadius: BorderRadius.circular(24),
                      child: InkWell(
                        borderRadius: BorderRadius.circular(24),
                        onTap: controller.isGenerating.value
                            ? null
                            : () => controller
                                .sendMessage(controller.textController.text),
                        child: Container(
                          padding: const EdgeInsets.all(12),
                          child: controller.isGenerating.value
                              ? const SizedBox(
                                  width: 24,
                                  height: 24,
                                  child: CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Icon(
                                  Icons.send,
                                  color: Colors.white,
                                ),
                        ),
                      ),
                    )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNovelSelector(
      BuildContext context, DaizongAIController controller) {
    final novels = Get.find<NovelController>().novels;

    return DialogSelector<Novel>(
      value: controller.selectedNovel.value,
      hint: '选择小说',
      title: '选择小说',
      searchable: true,
      searchHint: '搜索小说标题...',
      iconEnabledColor: forestGreen,
      items: novels.map<DialogSelectorItem<Novel>>((Novel novel) {
        return DialogSelectorItem<Novel>(
          value: novel,
          label: novel.title,
          subtitle: '${novel.chapters.length} 章节',
          leading: const Icon(Icons.book, size: 20),
          searchKeywords: [novel.title, novel.author ?? ''],
        );
      }).toList(),
      onChanged: (Novel? novel) {
        if (novel != null) {
          controller.selectNovel(novel);
        }
      },
    );
  }

  Widget _buildMessageBubble(
      BuildContext context, ChatMessage message, bool isDark) {
    return Align(
      alignment: message.isUser ? Alignment.centerRight : Alignment.centerLeft,
      child: Container(
        constraints: BoxConstraints(
          maxWidth: MediaQuery.of(context).size.width * 0.75,
        ),
        margin: const EdgeInsets.symmetric(vertical: 8),
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
        decoration: BoxDecoration(
          color: message.isUser
              ? forestGreen
              : message.isError
                  ? Colors.red.shade100
                  : (isDark
                      ? Colors.grey.shade700
                      : earthYellow.withAlpha(179)),
          borderRadius: BorderRadius.circular(18),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withAlpha(13),
              blurRadius: 2,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              message.isUser ? '我' : '岱宗AI',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 12,
                color: message.isUser
                    ? Colors.white70
                    : (isDark ? Colors.white70 : Colors.black54),
              ),
            ),
            const SizedBox(height: 4),
            _buildMessageContent(message, isDark),
          ],
        ),
      ),
    );
  }

  /// 构建消息内容（支持markdown）
  Widget _buildMessageContent(ChatMessage message, bool isDark) {
    final textColor = message.isUser
        ? Colors.white
        : message.isError
            ? Colors.red.shade900
            : (isDark ? Colors.white : Colors.black87);

    // 检查是否包含markdown语法
    if (!message.isUser && _containsMarkdown(message.content)) {
      return MarkdownBody(
        data: message.content,
        selectable: true,
        styleSheet: MarkdownStyleSheet(
          p: TextStyle(
            fontSize: 14,
            color: textColor,
            height: 1.4,
          ),
          h1: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
          h2: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
          h3: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
          strong: TextStyle(
            fontWeight: FontWeight.bold,
            color: textColor,
          ),
          em: TextStyle(
            fontStyle: FontStyle.italic,
            color: textColor,
          ),
          code: TextStyle(
            fontSize: 13,
            fontFamily: 'monospace',
            backgroundColor: isDark ? Colors.grey[800] : Colors.grey[200],
            color: textColor,
          ),
          codeblockDecoration: BoxDecoration(
            color: isDark ? Colors.grey[850] : Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: isDark ? Colors.grey[700]! : Colors.grey[300]!,
            ),
          ),
          blockquote: TextStyle(
            fontSize: 14,
            fontStyle: FontStyle.italic,
            color: textColor.withOpacity(0.8),
          ),
          blockquoteDecoration: BoxDecoration(
            border: Border(
              left: BorderSide(
                color: isDark ? Colors.blue[400]! : Colors.blue[600]!,
                width: 4,
              ),
            ),
          ),
          listBullet: TextStyle(
            color: textColor,
          ),
        ),
      );
    } else {
      return SelectableText(
        message.content,
        style: TextStyle(
          color: textColor,
        ),
      );
    }
  }

  /// 检查文本是否包含markdown语法
  bool _containsMarkdown(String text) {
    final markdownPatterns = [
      RegExp(r'\*\*.*?\*\*'), // 粗体
      RegExp(r'\*.*?\*'), // 斜体
      RegExp(r'`.*?`'), // 行内代码
      RegExp(r'```[\s\S]*?```'), // 代码块
      RegExp(r'^#{1,6}\s', multiLine: true), // 标题
      RegExp(r'^\s*[-*+]\s', multiLine: true), // 列表
      RegExp(r'^\s*\d+\.\s', multiLine: true), // 有序列表
      RegExp(r'\[.*?\]\(.*?\)'), // 链接
      RegExp(r'^>\s', multiLine: true), // 引用
    ];

    return markdownPatterns.any((pattern) => pattern.hasMatch(text));
  }
}
