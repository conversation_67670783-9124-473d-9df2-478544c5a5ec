import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// 弹窗选择器组件
/// 将下拉选择改为弹窗形式，提供更好的用户体验
class DialogSelector<T> extends StatelessWidget {
  final T? value;
  final String? hint;
  final List<DialogSelectorItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final Widget? icon;
  final Color? iconEnabledColor;
  final bool isExpanded;
  final String? title;
  final double? maxHeight;
  final bool searchable;
  final String? searchHint;

  const DialogSelector({
    super.key,
    this.value,
    this.hint,
    required this.items,
    this.onChanged,
    this.icon,
    this.iconEnabledColor,
    this.isExpanded = false,
    this.title,
    this.maxHeight,
    this.searchable = false,
    this.searchHint,
  });

  @override
  Widget build(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return InkWell(
      onTap: onChanged != null ? () => _showSelectionDialog(context) : null,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 16),
        decoration: BoxDecoration(
          border: Border.all(
            color: isDark ? Colors.grey.shade600 : Colors.grey.shade400,
          ),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Row(
          children: [
            Expanded(
              child: Text(
                _getDisplayText(),
                style: TextStyle(
                  color: value != null
                      ? (isDark ? Colors.white : Colors.black87)
                      : Colors.grey.shade600,
                  fontSize: 16,
                ),
              ),
            ),
            Icon(
              icon?.icon ?? Icons.arrow_drop_down,
              color: iconEnabledColor ??
                  (isDark ? Colors.grey.shade400 : Colors.grey.shade600),
            ),
          ],
        ),
      ),
    );
  }

  String _getDisplayText() {
    if (value != null) {
      final item = items.firstWhereOrNull((item) => item.value == value);
      return item?.label ?? value.toString();
    }
    return hint ?? '请选择';
  }

  void _showSelectionDialog(BuildContext context) {
    final searchController = TextEditingController();
    final filteredItems = <DialogSelectorItem<T>>[].obs;
    filteredItems.addAll(items);

    Get.dialog(
      Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: Container(
          width: MediaQuery.of(context).size.width * 0.8,
          constraints: BoxConstraints(
            maxHeight: maxHeight ?? MediaQuery.of(context).size.height * 0.6,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题栏
              Container(
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.primaryContainer,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        title ?? '请选择',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onPrimaryContainer,
                        ),
                      ),
                    ),
                    IconButton(
                      onPressed: () => Get.back(),
                      icon: Icon(
                        Icons.close,
                        color: Theme.of(context).colorScheme.onPrimaryContainer,
                      ),
                    ),
                  ],
                ),
              ),

              // 搜索框（如果启用）
              if (searchable) ...[
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: TextField(
                    controller: searchController,
                    decoration: InputDecoration(
                      hintText: searchHint ?? '搜索...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    onChanged: (query) {
                      filteredItems.clear();
                      if (query.isEmpty) {
                        filteredItems.addAll(items);
                      } else {
                        filteredItems.addAll(
                          items.where((item) =>
                            item.label.toLowerCase().contains(query.toLowerCase()) ||
                            (item.searchKeywords?.any((keyword) =>
                              keyword.toLowerCase().contains(query.toLowerCase())) ?? false)
                          ),
                        );
                      }
                    },
                  ),
                ),
              ],

              // 选项列表
              Flexible(
                child: Obx(() => ListView.builder(
                  shrinkWrap: true,
                  itemCount: filteredItems.length,
                  itemBuilder: (context, index) {
                    final item = filteredItems[index];
                    final isSelected = item.value == value;

                    return ListTile(
                      leading: item.leading,
                      title: Text(
                        item.label,
                        style: TextStyle(
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                          color: isSelected ? Theme.of(context).primaryColor : null,
                        ),
                      ),
                      subtitle: item.subtitle != null ? Text(item.subtitle!) : null,
                      trailing: isSelected
                          ? Icon(Icons.check, color: Theme.of(context).primaryColor)
                          : item.trailing,
                      selected: isSelected,
                      onTap: () {
                        Get.back();
                        onChanged?.call(item.value);
                      },
                    );
                  },
                )),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// 弹窗选择器选项数据类
class DialogSelectorItem<T> {
  final T value;
  final String label;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final List<String>? searchKeywords;

  const DialogSelectorItem({
    required this.value,
    required this.label,
    this.subtitle,
    this.leading,
    this.trailing,
    this.searchKeywords,
  });
}

/// 带有主题的下拉按钮组件（保持向后兼容）
/// 确保在所有主题下都有正确的背景色
class ThemedDropdownButton<T> extends StatelessWidget {
  final T? value;
  final String? hint;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final Widget? icon;
  final Color? iconEnabledColor;
  final Widget? underline;
  final bool isExpanded;
  final double? itemHeight;
  final FocusNode? focusNode;
  final bool autofocus;
  final Color? dropdownColor;
  final bool useDialog; // 新增：是否使用弹窗模式

  const ThemedDropdownButton({
    super.key,
    this.value,
    this.hint,
    required this.items,
    this.onChanged,
    this.icon,
    this.iconEnabledColor,
    this.underline,
    this.isExpanded = false,
    this.itemHeight,
    this.focusNode,
    this.autofocus = false,
    this.dropdownColor,
    this.useDialog = true, // 默认使用弹窗模式
  });

  @override
  Widget build(BuildContext context) {
    if (useDialog) {
      // 使用弹窗选择器
      return DialogSelector<T>(
        value: value,
        hint: hint,
        items: items.map((item) => DialogSelectorItem<T>(
          value: item.value!,
          label: _extractTextFromWidget(item.child),
        )).toList(),
        onChanged: onChanged,
        icon: icon,
        iconEnabledColor: iconEnabledColor,
        title: hint,
      );
    }

    // 保持原有的下拉框实现
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return DropdownButton<T>(
      value: value,
      hint: hint != null ? Text(hint!) : null,
      items: items,
      onChanged: onChanged,
      icon: icon,
      iconEnabledColor: iconEnabledColor,
      underline: underline,
      isExpanded: isExpanded,
      itemHeight: itemHeight,
      focusNode: focusNode,
      autofocus: autofocus,
      dropdownColor: dropdownColor ??
          (isDark ? const Color(0xFF2A2A2A) : Colors.white),
    );
  }

  String _extractTextFromWidget(Widget widget) {
    if (widget is Text) {
      return widget.data ?? '';
    } else if (widget is RichText) {
      return widget.text.toPlainText();
    } else if (widget is Container && widget.child != null) {
      return _extractTextFromWidget(widget.child!);
    } else if (widget is Padding && widget.child != null) {
      return _extractTextFromWidget(widget.child!);
    } else if (widget is Center && widget.child != null) {
      return _extractTextFromWidget(widget.child!);
    } else if (widget is SizedBox && widget.child != null) {
      return _extractTextFromWidget(widget.child!);
    }
    return widget.toString();
  }
}

/// 弹窗选择器表单字段组件
class DialogSelectorFormField<T> extends StatelessWidget {
  final T? value;
  final List<DialogSelectorItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final InputDecoration? decoration;
  final FormFieldValidator<T>? validator;
  final FormFieldSetter<T>? onSaved;
  final Widget? icon;
  final Color? iconEnabledColor;
  final String? title;
  final double? maxHeight;
  final bool searchable;
  final String? searchHint;

  const DialogSelectorFormField({
    super.key,
    this.value,
    required this.items,
    this.onChanged,
    this.decoration,
    this.validator,
    this.onSaved,
    this.icon,
    this.iconEnabledColor,
    this.title,
    this.maxHeight,
    this.searchable = false,
    this.searchHint,
  });

  @override
  Widget build(BuildContext context) {
    return FormField<T>(
      initialValue: value,
      validator: validator,
      onSaved: onSaved,
      builder: (FormFieldState<T> state) {
        return InputDecorator(
          decoration: (decoration ?? const InputDecoration()).copyWith(
            errorText: state.errorText,
          ),
          child: DialogSelector<T>(
            value: state.value,
            items: items,
            onChanged: (T? newValue) {
              state.didChange(newValue);
              onChanged?.call(newValue);
            },
            icon: icon,
            iconEnabledColor: iconEnabledColor,
            title: title ?? decoration?.labelText,
            maxHeight: maxHeight,
            searchable: searchable,
            searchHint: searchHint,
          ),
        );
      },
    );
  }
}

/// 带有主题的下拉按钮表单字段组件（保持向后兼容）
class ThemedDropdownButtonFormField<T> extends StatelessWidget {
  final T? value;
  final List<DropdownMenuItem<T>> items;
  final ValueChanged<T?>? onChanged;
  final InputDecoration? decoration;
  final FormFieldValidator<T>? validator;
  final FormFieldSetter<T>? onSaved;
  final Widget? icon;
  final Color? iconEnabledColor;
  final bool isExpanded;
  final double? itemHeight;
  final FocusNode? focusNode;
  final bool autofocus;
  final Color? dropdownColor;
  final bool useDialog; // 新增：是否使用弹窗模式
  final bool searchable; // 新增：是否支持搜索
  final String? searchHint; // 新增：搜索提示

  const ThemedDropdownButtonFormField({
    super.key,
    this.value,
    required this.items,
    this.onChanged,
    this.decoration,
    this.validator,
    this.onSaved,
    this.icon,
    this.iconEnabledColor,
    this.isExpanded = false,
    this.itemHeight,
    this.focusNode,
    this.autofocus = false,
    this.dropdownColor,
    this.useDialog = true, // 默认使用弹窗模式
    this.searchable = false,
    this.searchHint,
  });

  @override
  Widget build(BuildContext context) {
    if (useDialog) {
      // 使用弹窗选择器表单字段
      return DialogSelectorFormField<T>(
        value: value,
        items: items.map((item) => DialogSelectorItem<T>(
          value: item.value!,
          label: _extractTextFromWidget(item.child),
        )).toList(),
        onChanged: onChanged,
        decoration: decoration,
        validator: validator,
        onSaved: onSaved,
        icon: icon,
        iconEnabledColor: iconEnabledColor,
        title: decoration?.labelText,
        searchable: searchable,
        searchHint: searchHint,
      );
    }

    // 保持原有的下拉框表单字段实现
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return DropdownButtonFormField<T>(
      value: value,
      items: items,
      onChanged: onChanged,
      decoration: decoration,
      validator: validator,
      onSaved: onSaved,
      icon: icon,
      iconEnabledColor: iconEnabledColor,
      isExpanded: isExpanded,
      itemHeight: itemHeight,
      focusNode: focusNode,
      autofocus: autofocus,
      dropdownColor: dropdownColor ??
          (isDark ? const Color(0xFF2A2A2A) : Colors.white),
    );
  }

  String _extractTextFromWidget(Widget widget) {
    if (widget is Text) {
      return widget.data ?? '';
    } else if (widget is RichText) {
      return widget.text.toPlainText();
    } else if (widget is Container && widget.child != null) {
      return _extractTextFromWidget(widget.child!);
    } else if (widget is Padding && widget.child != null) {
      return _extractTextFromWidget(widget.child!);
    } else if (widget is Center && widget.child != null) {
      return _extractTextFromWidget(widget.child!);
    } else if (widget is SizedBox && widget.child != null) {
      return _extractTextFromWidget(widget.child!);
    }
    return widget.toString();
  }
}
